"""
Main Volatility Mean Reversion Trading System
The Architect's complete implementation - this is what actually makes money
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

from data_collectors.vix_collector import VIXDataCollector
from strategies.volatility_mean_reversion import VolatilityMeanReversionStrategy
from risk_management.volatility_risk_manager import VolatilityRiskManager
from paper_trading.volatility_paper_trader import VolatilityPaperTrader
from utils.cache import cache_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VolatilityTradingSystem:
    """
    The Architect's Complete Volatility Trading System
    
    This is the real deal - not some academic exercise
    """
    
    def __init__(self, portfolio_size: float = 1000000):
        self.portfolio_size = portfolio_size
        
        # Initialize components
        self.vix_collector = VIXDataCollector()
        self.strategy = VolatilityMeanReversionStrategy(portfolio_size)
        self.risk_manager = VolatilityRiskManager(portfolio_size)
        self.paper_trader = VolatilityPaperTrader(portfolio_size)
        self.cache = cache_manager
        
        # System state
        self.is_running = False
        self.last_signal_time = None
        self.daily_signals_generated = 0
        self.max_daily_signals = 5  # Limit signal generation
        
        # Performance tracking
        self.system_start_time = None
        self.total_signals_generated = 0
        self.total_trades_executed = 0
        
    async def start_system(self):
        """
        Start the volatility trading system
        The Architect: This is where we begin making money
        """
        logger.info("=" * 60)
        logger.info("THE ARCHITECT'S VOLATILITY TRADING SYSTEM")
        logger.info("=" * 60)
        logger.info(f"Portfolio Size: ₹{self.portfolio_size:,.0f}")
        logger.info(f"Max Position Risk: {self.risk_manager.max_position_risk:.1%}")
        logger.info(f"Max Daily Risk: {self.risk_manager.max_daily_risk:.1%}")
        logger.info("=" * 60)
        
        self.is_running = True
        self.system_start_time = datetime.now()
        
        # Reset daily metrics
        self.risk_manager.reset_daily_metrics()
        
        try:
            # Main trading loop
            while self.is_running:
                await self._trading_cycle()
                await asyncio.sleep(60)  # Run every minute
                
        except KeyboardInterrupt:
            logger.info("System shutdown requested")
        except Exception as e:
            logger.error(f"System error: {e}")
        finally:
            await self.stop_system()
    
    async def _trading_cycle(self):
        """
        Execute one complete trading cycle
        The Architect: This is the heartbeat of the system
        """
        try:
            # Check if trading should be halted
            should_halt, reason = self.risk_manager.should_halt_trading()
            if should_halt:
                logger.warning(f"Trading halted: {reason}")
                return
            
            # Check daily signal limit
            if self.daily_signals_generated >= self.max_daily_signals:
                logger.info("Daily signal limit reached")
                return
            
            # Generate trading signals
            signals = await self.strategy.generate_signals()
            
            if signals:
                logger.info(f"Generated {len(signals)} trading signals")
                
                for signal in signals:
                    await self._process_signal(signal)
                    
                self.daily_signals_generated += len(signals)
                self.total_signals_generated += len(signals)
            
            # Update existing positions
            await self._update_positions()
            
            # Log system status every 10 minutes
            if datetime.now().minute % 10 == 0:
                await self._log_system_status()
                
        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")
    
    async def _process_signal(self, signal: Dict[str, Any]):
        """
        Process a trading signal through risk management and execution
        The Architect: This is where discipline meets opportunity
        """
        try:
            logger.info(f"Processing signal: {signal['strategy']} - Confidence: {signal['confidence']:.1%}")
            
            # Validate signal through risk management
            is_valid, validation_message = self.risk_manager.validate_new_position(signal)
            
            if not is_valid:
                logger.warning(f"Signal rejected: {validation_message}")
                return
            
            # Calculate optimal position size
            optimal_lots = self.risk_manager.calculate_position_size(signal)
            
            if optimal_lots <= 0:
                logger.warning("Position size calculation resulted in 0 lots")
                return
            
            # Update signal with optimal sizing
            signal["lots"] = optimal_lots
            
            # Execute trade through paper trader
            executed_position = await self.paper_trader.execute_volatility_trade(signal)
            
            if executed_position:
                # Add position to risk manager tracking
                self.risk_manager.add_position(executed_position)
                self.total_trades_executed += 1
                
                logger.info(f"Trade executed: {executed_position['strategy']} - {optimal_lots} lots")
                logger.info(f"Entry premium: ₹{executed_position['entry_premium']:.2f}")
                
                # Update last signal time
                self.last_signal_time = datetime.now()
            else:
                logger.warning("Failed to execute trade")
                
        except Exception as e:
            logger.error(f"Error processing signal: {e}")
    
    async def _update_positions(self):
        """
        Update all active positions with current market prices
        The Architect: Keep track of your money
        """
        try:
            if not self.paper_trader.active_positions:
                return
            
            # Get current option prices (simplified - in production use real data)
            current_prices = await self._get_current_option_prices()
            
            # Update positions
            await self.paper_trader.update_positions(current_prices)
            
        except Exception as e:
            logger.error(f"Error updating positions: {e}")
    
    async def _get_current_option_prices(self) -> Dict:
        """
        Get current option prices for position updates
        The Architect: In production, this would be real-time data
        """
        # Simplified price simulation for demo
        # In production, this would fetch real NSE option prices
        import random
        
        current_prices = {}
        
        for position in self.paper_trader.active_positions.values():
            for leg in position["legs"]:
                key = (leg["strike"], leg["expiry"], leg["option_type"])
                
                # Simulate price movement (±5% random walk)
                base_price = leg["execution_price"]
                price_change = random.uniform(-0.05, 0.05)
                current_price = base_price * (1 + price_change)
                current_prices[key] = max(0.05, current_price)  # Minimum ₹0.05
        
        return current_prices
    
    async def _log_system_status(self):
        """
        Log comprehensive system status
        The Architect: Know where you stand
        """
        try:
            # Get performance summary
            performance = self.paper_trader.get_performance_summary()
            
            # Get risk status
            risk_status = self.risk_manager.get_risk_status()
            
            logger.info("=" * 50)
            logger.info("SYSTEM STATUS")
            logger.info("=" * 50)
            logger.info(f"Portfolio Value: ₹{performance['current_portfolio_value']:,.2f}")
            logger.info(f"Total P&L: ₹{performance['total_pnl']:,.2f} ({performance['total_return_pct']:+.2f}%)")
            logger.info(f"Max Drawdown: {performance['max_drawdown_pct']:.2f}%")
            logger.info(f"Win Rate: {performance['win_rate_pct']:.1f}%")
            logger.info(f"Sharpe Ratio: {performance['sharpe_ratio']:.2f}")
            logger.info(f"Active Positions: {performance['active_positions']}")
            logger.info(f"Total Trades: {performance['total_trades']}")
            logger.info(f"Daily P&L: ₹{risk_status['daily_pnl']:,.2f}")
            logger.info(f"Available Cash: ₹{performance['available_cash']:,.2f}")
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"Error logging system status: {e}")
    
    async def stop_system(self):
        """
        Gracefully stop the trading system
        The Architect: Always have an exit plan
        """
        logger.info("Stopping volatility trading system...")
        
        self.is_running = False
        
        # Close all active positions
        for position_id in list(self.paper_trader.active_positions.keys()):
            await self.paper_trader.close_position(position_id, "System shutdown")
        
        # Final performance report
        await self._generate_final_report()
        
        logger.info("System stopped successfully")
    
    async def _generate_final_report(self):
        """
        Generate final performance report
        The Architect: This is your report card
        """
        try:
            performance = self.paper_trader.get_performance_summary()
            
            runtime = datetime.now() - self.system_start_time if self.system_start_time else timedelta(0)
            
            logger.info("=" * 60)
            logger.info("FINAL PERFORMANCE REPORT")
            logger.info("=" * 60)
            logger.info(f"Runtime: {runtime}")
            logger.info(f"Initial Capital: ₹{performance['initial_capital']:,.2f}")
            logger.info(f"Final Portfolio Value: ₹{performance['current_portfolio_value']:,.2f}")
            logger.info(f"Total Return: {performance['total_return_pct']:+.2f}%")
            logger.info(f"Total P&L: ₹{performance['total_pnl']:,.2f}")
            logger.info(f"Max Drawdown: {performance['max_drawdown_pct']:.2f}%")
            logger.info(f"Total Trades: {performance['total_trades']}")
            logger.info(f"Winning Trades: {performance['winning_trades']}")
            logger.info(f"Win Rate: {performance['win_rate_pct']:.1f}%")
            logger.info(f"Sharpe Ratio: {performance['sharpe_ratio']:.2f}")
            logger.info(f"Signals Generated: {self.total_signals_generated}")
            logger.info(f"Trades Executed: {self.total_trades_executed}")
            logger.info("=" * 60)
            
            # Save report to file
            report_data = {
                "performance": performance,
                "system_stats": {
                    "runtime_seconds": runtime.total_seconds(),
                    "signals_generated": self.total_signals_generated,
                    "trades_executed": self.total_trades_executed
                },
                "timestamp": datetime.now().isoformat()
            }
            
            with open(f"volatility_trading_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
                json.dump(report_data, f, indent=2)
            
            logger.info("Performance report saved to file")
            
        except Exception as e:
            logger.error(f"Error generating final report: {e}")

async def main():
    """
    Main entry point for the volatility trading system
    The Architect: Let's make some money
    """
    # Initialize system with 10 lakh portfolio
    system = VolatilityTradingSystem(portfolio_size=1000000)
    
    try:
        await system.start_system()
    except KeyboardInterrupt:
        logger.info("Shutdown requested by user")
    except Exception as e:
        logger.error(f"System error: {e}")
    finally:
        await system.stop_system()

if __name__ == "__main__":
    asyncio.run(main())
